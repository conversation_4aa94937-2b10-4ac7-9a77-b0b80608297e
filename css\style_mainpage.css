@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap');

body{   
    background-color: rgb(11, 8, 16);
    animation: visibility .5s 1;
}   

@keyframes visibility{
    0%{
        opacity: 0;

    }
    100%{
        opacity: 1;

    }
    
}

p{
    margin: 0px;
}

.header{

    display: flex;
    justify-content: space-between;
    min-width: 100%;
    min-height: 15vh;
}

.box-cont1{
    margin-left: 1.5%;
    display: flex;
    align-items: flex-end;
}

.box-cont1 p{
    color: white;
    font-size: 2.5em;
    font-family: "Cinzel", "sans-serif";
}

.box-cont2{
    margin-right: 1.5%;
    max-height: 100px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.box-cont2-bin{
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    margin: 0px 10px;
    width: fit-content;
    height: auto;
}

.profile-icon{
    filter: invert(100%);
}

    .toggle-profile {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    right: 0;
    background-color: #111;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
    text-align: center;
    font-family: Arial, Helvetica, sans-serif;
    }

    .box-cont-exp{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .box-cont-exp .user_exp {
        color: white;
        font-size: 2.5em;
        font-family: "Cinzel", "sans-serif";
        margin: 0px 0px 0px 0px;
    }

    .box-cont-exp .exp_label{
        color: white;
        font-size: 1em;
        font-family: "Cinzel", "sans-serif";
        margin: 0px 0px 0px 0px;
    }

    .toggle-profile a, .toggle-profile p{
    margin: 20px 0px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
    }   

    .toggle-profile p{
        font-size: 18px;
    }

    .toggle-profile img{
    width: 100px;
    filter: invert(85%);
    }

    .toggle-profile a:hover {
    color: #f1f1f1;
    }

    .toggle-profile .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 40px;
    margin-left: 50px;
    }  

.box-cont2-bin:hover{
    transform: scale(1.1);
    transition: 0.5s;
}

.box-cont2-bin p{
    color: white;
    font-size: .8em;
    font-family: "Cinzel", "sans-serif";
}

.box-cont2-bin img{
    width: auto;
    object-fit: contain;
    height: 50px;

}


.body{
    display: flex;
    justify-content: center;
    align-items: flex-start;

    min-height: 55vh;
    height: auto;
}

.cont-levels{
    margin: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.cont-levels-row{
    display: grid;
    width: 50%;
    grid-template-columns: repeat(auto-fill, 120px);
    gap: 20px;
    justify-content: center;
}

.cont-levels-column a{
    text-decoration: none;
    color: white;
}

.cont-levels-column button{
    background-color: rgb(15, 0, 227);
    color: white;
    font-size: 3em;
    font-family: "Cinzel", "sans-serif";
    width: 120px;
    height: 120px;
    border-radius: 50%;
    cursor: pointer;
    border: none;

}

.cont-levels-column{
    position: relative;
}

/* Style for locked levels */
.cont-levels-column button.locked {
    background-color: #818181;
    opacity: 0.7;
    cursor: not-allowed;
}

.locked-level {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.locked-level img {
    width: 50%;
    height: 50%;
    margin-bottom: 5px;
    filter: grayscale(100%);
}

.locked-level span {
    font-size: 1.2em;
}

.level-stars {
    position: relative;

    color: #ffeb3b;
    font-size: .4em;
}

.level-score {
    position: absolute;
    bottom: 5px;
    right: 5px;
    color: #fff;
    font-size: 0.8rem;
}

/* Style for locked levels */

/* Achievement Notification Styles */
.achievement-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transform: translateX(120%);
    animation: slideIn 0.5s forwards;
    max-width: 350px;
    width: 100%;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

.achievement-notification.fade-out {
    animation: slideOut 0.5s forwards;
}

@keyframes slideOut {
    to {
        transform: translateX(120%);
    }
}

.notification-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.achievement-icon {
    position: relative;
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.trophy {
    display: block;
    animation: bounce 1s ease infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.sparkles {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    pointer-events: none;
}

.sparkle {
    position: absolute;
    font-size: 1rem;
    animation: sparkle 2s ease infinite;
}

.sparkle:nth-child(1) {
    top: 0;
    left: 50%;
    animation-delay: 0s;
}

.sparkle:nth-child(2) {
    top: 50%;
    right: 0;
    animation-delay: 0.5s;
}

.sparkle:nth-child(3) {
    bottom: 0;
    left: 25%;
    animation-delay: 1s;
}

@keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
}

.achievement-text {
    text-align: center;
}

.achievement-text h3 {
    color: #fff;
    font-family: "Cinzel", sans-serif;
    font-size: 1.2rem;
    margin: 0 0 5px 0;
}

.achievement-text p {
    color: #b8b8b8;
    font-size: 0.9rem;
    margin: 0;
}

.view-achievements-btn {
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 20px;
    font-family: "Cinzel", sans-serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.view-achievements-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 0, 224, 0.4);
}