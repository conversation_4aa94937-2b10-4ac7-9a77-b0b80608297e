// Function to handle form submission
document.getElementById('profile-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Gather form data
    const formData = {
        username: document.getElementById('username').value,
        email: document.getElementById('email').value,
        bio: document.getElementById('bio').value
    };
    
    saveProfileChanges(formData);
});

// Function to show centered confirmation modal and disable background
function showConfirmationModal(formData) {
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';
    
    // Create modal container
    const modal = document.createElement('div');
    modal.className = 'confirmation-modal';
    modal.innerHTML = ` 
        <div class="modal-content">
            <h3>Confirm Changes</h3>
            <p>Are you sure you want to save these changes?</p>
            <div class="modal-actions">
                <button id="confirm-save" class="btn-confirm">💾 Yes</button>
                <button id="cancel-save" class="btn-cancel">🚫 No</button>
            </div>
        </div>
    `;
    
    // Disable all focusable elements in the background
    const focusableElements = document.querySelectorAll('button, input, select, textarea, a[href]');
    const previouslyFocused = document.activeElement;
    focusableElements.forEach(el => el.setAttribute('tabindex', '-1'));
    
    // Append elements to DOM
    modalOverlay.appendChild(modal);
    document.body.appendChild(modalOverlay);
    document.body.style.overflow = 'hidden'; // Prevent scrolling
    
    // Focus the first button in modal for accessibility
    document.getElementById('confirm-save').focus();
    
    // Add event listeners
    document.getElementById('confirm-save').addEventListener('click', () => {
        saveProfileChanges(formData);
        closeModal();
    });
    
    document.getElementById('cancel-save').addEventListener('click', closeModal);
    
    // Close modal when pressing Escape
    document.addEventListener('keydown', handleEscapeKey);
    
    function closeModal() {
        // Restore focusability
        focusableElements.forEach(el => el.removeAttribute('tabindex'));
        if (previouslyFocused) previouslyFocused.focus();
        
        // Clean up
        document.body.style.overflow = '';
        document.removeEventListener('keydown', handleEscapeKey);
        modalOverlay.remove();
    }
    
    function handleEscapeKey(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    }
}   

function extractAvatarUrl(avatarElement) {
    if (!avatarElement) return '';
    
    const bgImage = window.getComputedStyle(avatarElement).backgroundImage;
    
    // Handle cases: url("..."), url('...'), url(...)
    const urlMatch = bgImage.match(/^url\((["']?)(.*?)\1\)$/);
    if (urlMatch && urlMatch[2]) {
        return urlMatch[2];
    }
    
    return '';
}

function normalizeAvatarUrl(url) {
    if (!url) return '';
    
    // Convert relative paths to absolute
    if (!url.startsWith('http') && !url.startsWith('data:image') && !url.startsWith('/')) {
        return `${window.location.origin}/${url.replace(/^\.?\//, '')}`;
    }
    
    return url;
}

function saveProfileChanges(formData) {
    const avatarElement = document.querySelector('.avatar');
    const avatarUrl = extractAvatarUrl(avatarElement);
    
    // Add normalized avatar URL to form data
    formData.avatar = normalizeAvatarUrl(avatarUrl);

    fetch('../php/save_profile.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
    })
    .then(handleResponse)
    .then(data => {
        if (data.success) {
            alert('Profile saved successfully!');
            updateCompletionBadges(formData);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving profile: ' + error.message);
    });
}

function handleResponse(response) {
    if (!response.ok) {
        return response.text().then(text => {
            throw new Error(text || 'Network response was not ok');
        });
    }
    return response.json().catch(() => {
        throw new Error('Invalid JSON response');
    });
}

// Modified loadProfileData function to load avatar
function loadProfileData() {
    fetch('../php/get_profile.php')
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Populate form fields with fetched data
            const usernameField = document.getElementById('username');
            usernameField.value = data.username || '';
            
            // Disable username field if it already has a value
            if (data.username && data.username.trim() !== '') {
                usernameField.readOnly = true;
                usernameField.classList.add('readonly-field');
                
                // Add a tooltip or explanation
                const usernameLabel = document.querySelector('label[for="username"]');
                const lockIcon = document.createElement('span');
                lockIcon.innerHTML = ' 🔒';
                lockIcon.title = 'Username cannot be changed';
                usernameLabel.appendChild(lockIcon);
            }
            
            document.getElementById('email').value = data.email || '';
            document.getElementById('bio').value = data.bio || '';
            
            // Set avatar if exists
            if (data.avatar) {
                const avatarElement = document.querySelector('.avatar');
                if (avatarElement) {
                    avatarElement.style.backgroundImage = `url('${data.avatar}')`;
                }
            }
            
            // Update completion badges
            updateCompletionBadges(data);
        } else {
            console.error('Error loading profile:', data.message);
        }
    })
    .catch(error => {
        console.error('Error loading profile:', error);
    });
}

// Function to update completion badges (example implementation)
function updateCompletionBadges(formData) {
    // Update username badge
    const usernameBadge = document.querySelector('#username ~ .input-badge');
    if (formData.username && formData.username.trim() !== '') {
        usernameBadge.classList.remove('incomplete');
        usernameBadge.classList.add('complete');
        usernameBadge.textContent = '✓';
    } else {
        usernameBadge.classList.remove('complete');
        usernameBadge.classList.add('incomplete');
        usernameBadge.textContent = '!';
    }

    // Update email badge
    const emailBadge = document.querySelector('#email ~ .input-badge');
    if (formData.email && formData.email.trim() !== '') {
        emailBadge.classList.remove('incomplete');
        emailBadge.classList.add('complete');
        emailBadge.textContent = '✓';
    } else {
        emailBadge.classList.remove('complete');
        emailBadge.classList.add('incomplete');
        emailBadge.textContent = '!';
    }

    // Update bio badge
    const bioBadge = document.querySelector('#bio ~ .input-badge');
    if (formData.bio && formData.bio.trim() !== '') {
        bioBadge.classList.remove('incomplete');
        bioBadge.classList.add('complete');
        bioBadge.textContent = '✓';
    } else {
        bioBadge.classList.remove('complete');
        bioBadge.classList.add('incomplete');
        bioBadge.textContent = '!';
    }

    // Update completion meter (simple example)
    let completion = 0;
    if (formData.username && formData.username.trim() !== '') completion += 33;
    if (formData.email && formData.email.trim() !== '') completion += 33;
    if (formData.bio && formData.bio.trim() !== '') completion += 34;
    
    const meterFill = document.querySelector('.meter-fill');
    const meterText = document.querySelector('.completion-meter span:last-child');
    meterFill.style.width = `${completion}%`;
    meterText.textContent = `${completion}%`;
}   

// Avatar Selection Modal
function setupAvatarModal() {
    const editBtn = document.querySelector('.btn-edit');
    if (!editBtn) return;

    editBtn.addEventListener('click', (e) => {
        e.preventDefault();
        showAvatarModal();
    });
}
function showAvatarModal() {
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';
    
    // Avatar options
    const avatars = [
        '../images/profiles/avatar1.png',
        '../images/profiles/avatar2.png',
        '../images/profiles/avatar3.png',
        '../images/profiles/avatar4.png',
        '../images/profiles/avatar5.png',
        '../images/profiles/avatar6.png',
        '../images/profiles/avatar7.png',
        '../images/profiles/avatar8.png',
        '../images/profiles/avatar9.png',
    ];
    
    // Create modal container
    const modal = document.createElement('div');
    modal.className = 'avatar-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>Choose Your Avatar</h3>
            <div class="upload-section">
                <input type="file" id="avatar-upload" accept="image/*" class="avatar-upload-input">
                <label for="avatar-upload" class="avatar-upload-btn">📤 Upload Your Avatar</label>
                <div id="upload-preview" class="avatar-upload-preview"></div>
            </div>
            <div class="avatar-grid">
                ${avatars.map(avatar => `
                    <div class="avatar-option" data-url="${avatar}">
                        <img src="${avatar}" alt="Avatar" class="avatar-option-img">
                    </div>
                `).join('')}
            </div>
            <div class="modal-actions">
                <button id="cancel-avatar" class="avatar-modal-cancel-btn">🚫 Cancel</button>
            </div>
        </div>
    `;
    
    // Disable background elements
    disableBackground();
    
    // Append elements to DOM
    modalOverlay.appendChild(modal);
    document.body.appendChild(modalOverlay);
    document.body.classList.add('modal-open');
    
    // Event handler for Escape key
    const handleEscapeKey = (e) => {
        if (e.key === 'Escape') {
            closeModal();
        }
    };
    
    // Close modal function
    const closeModal = () => {
        enableBackground();
        document.body.classList.remove('modal-open');
        document.removeEventListener('keydown', handleEscapeKey);
        modalOverlay.remove();
    };
    
    // Function to show confirmation modal
    function showConfirmationModal(avatarUrl) {
        const confirmationModal = document.createElement('div');
        confirmationModal.className = 'avatar-confirmation-overlay';
        confirmationModal.innerHTML = `
            <div class="avatar-confirmation-modal">
                <h3 class="avatar-confirmation-title">Confirm Avatar Selection</h3>
                <div class="avatar-confirmation-preview">
                    <div class="avatar-confirmation-img" style="background-image: url('${avatarUrl}')"></div>
                </div>
                <p class="avatar-confirmation-text">Are you sure you want to use this avatar?</p>
                <div class="avatar-confirmation-actions">
                    <button id="confirm-selection" class="avatar-confirm-btn">✅ Yes, Use This Avatar</button>
                    <button id="cancel-selection" class="avatar-cancel-btn">❌ No, Choose Another</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(confirmationModal);
        
        // Handle confirmation
        const confirmBtn = confirmationModal.querySelector('#confirm-selection');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                document.querySelector('.avatar').style.backgroundImage = `url('${avatarUrl}')`;
                confirmationModal.remove();
                closeModal();
            });
        }
        
        // Handle cancellation
        const cancelBtn = confirmationModal.querySelector('#cancel-selection');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                confirmationModal.remove();
            });
        }
        
        // Close when clicking outside
        // confirmationModal.addEventListener('click', (e) => {
        //     if (e.target === confirmationModal) {
        //         confirmationModal.remove();
        //     }
        // });
    }
    
    // Handle avatar selection from grid
    const avatarOptions = modal.querySelectorAll('.avatar-option');
    avatarOptions.forEach(option => {
        option.addEventListener('click', () => {
            const avatarUrl = option.dataset.url;
            showConfirmationModal(avatarUrl);
        });
    });
    
   
    // Handle file upload
    const uploadInput = modal.querySelector('#avatar-upload');
    const uploadPreview = modal.querySelector('#upload-preview');
    
    if (uploadInput && uploadPreview) {
        uploadInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                
                reader.onload = (event) => {
                    // Immediately show confirmation modal with the uploaded image
                    showConfirmationModal(event.target.result);
                    
                    // Optional: You can still show the preview if you want
                    // uploadPreview.innerHTML = `
                    //     <div class="avatar-option" data-url="${event.target.result}">
                    //         <img src="${event.target.result}" alt="Uploaded Avatar" class="avatar-option-img">
                    //         <p class="avatar-option-text">Uploaded Avatar</p>
                    //     </div>
                    // `;
                    // uploadPreview.style.display = 'block';
                };
                
                reader.readAsDataURL(file);
            }
        });
    }
    
    const cancelBtn = modal.querySelector('#cancel-avatar');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }
    
    // Close modal when clicking outside
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            closeModal();
        }
    });
    
    // Add Escape key listener
    document.addEventListener('keydown', handleEscapeKey);
}

function disableBackground() {
    // Disable keyboard focus
    const focusableElements = document.querySelectorAll('button, input, select, textarea, a[href]');
    focusableElements.forEach(el => el.setAttribute('tabindex', '-1'));
    
    // Disable pointer events on the overlay
    document.querySelectorAll('.modal-overlay').forEach(overlay => {
        overlay.style.pointerEvents = 'none';
    });
}

function enableBackground() {
    // Re-enable keyboard focus
    const focusableElements = document.querySelectorAll('button, input, select, textarea, a[href]');
    focusableElements.forEach(el => el.removeAttribute('tabindex'));
    
    // Re-enable pointer events on the overlay
    document.querySelectorAll('.modal-overlay').forEach(overlay => {
        overlay.style.pointerEvents = 'auto';
    });
}

// Initialize the avatar modal when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    setupAvatarModal();
    loadProfileData();
});

// Function to handle avatar deletion
function handleAvatarDeletion() {
    const avatarElement = document.querySelector('.avatar');
    if (!avatarElement) return;

    // Show confirmation modal
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';
    
    const modal = document.createElement('div');
    modal.className = 'confirmation-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>Delete Avatar</h3>
            <p>Are you sure you want to delete your avatar?</p>
            <div class="modal-actions">
                <button id="confirm-delete" class="btn-confirm">🗑️ Yes</button>
                <button id="cancel-delete" class="btn-cancel">🚫 No</button>
            </div>
        </div>
    `;
    
    modalOverlay.appendChild(modal);
    document.body.appendChild(modalOverlay);
    document.body.style.overflow = 'hidden';
    document.body.classList.add('modal-open');

    // Disable background elements
    disableBackground();

    // Focus the first button in modal for accessibility
    document.getElementById('confirm-delete').focus();

    // Add event listeners
    const confirmDelete = document.getElementById('confirm-delete');
    const cancelDelete = document.getElementById('cancel-delete');

    confirmDelete.addEventListener('click', () => {
        // Reset avatar to default
        avatarElement.style.backgroundImage = "url('https://i.pravatar.cc/150')";
        
        // Save the change to the server
        const formData = {
            username: document.getElementById('username').value,
            email: document.getElementById('email').value,
            bio: document.getElementById('bio').value,
            avatar: '' // Empty string to indicate deletion
        };
        
        saveProfileChanges(formData);
        closeModal();
    });

    cancelDelete.addEventListener('click', closeModal);

    // Close modal when pressing Escape
    document.addEventListener('keydown', handleEscapeKey);

    function closeModal() {
        // Re-enable background elements
        enableBackground();
        document.body.style.overflow = '';
        document.body.classList.remove('modal-open');
        document.removeEventListener('keydown', handleEscapeKey);
        modalOverlay.remove();
    }

    function handleEscapeKey(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    }

    // Prevent click events from bubbling up to the overlay
    modal.addEventListener('click', (e) => {
        e.stopPropagation();
    });

    // Close modal when clicking outside
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            closeModal();
        }
    });
}

// Add event listener for delete button
document.addEventListener('DOMContentLoaded', function() {
    const deleteBtn = document.querySelector('.btn-delete');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', handleAvatarDeletion);
    }
});
