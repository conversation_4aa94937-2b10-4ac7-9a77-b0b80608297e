<?php
// Disable error display to prevent HTML output
ini_set('display_errors', 0);
error_reporting(0);

// Set headers first
header('Content-Type: application/json');

try {
    require_once 'dbconnection.php';
    
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Prepare and execute the query
    $stmt = $conn->prepare("SELECT *
                           FROM game_content 
                           WHERE level_number = 2 
                           ORDER BY RAND()");
    $stmt->execute();
    
    // Fetch all results
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return the results as JSON
    echo json_encode([
        'success' => true,
        'data' => $results
    ]);
    
} catch(PDOException $e) {
    // Return error message as JSON
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch(Exception $e) {
    // Catch any other errors
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
