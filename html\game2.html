<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Robot Rescue Quiz - Answer questions correctly to save the robot!">
    <title>Robot Rescue Quiz</title>
    <link rel="stylesheet" href="../css/style_game1.css">
</head>
<body data-level="2">
    <div class="game-container">
        <!-- Instruction Modal -->
        <div class="modal" id="instruction-modal" role="dialog" aria-labelledby="instruction-title">
            <div class="modal-content">
                <h2 id="instruction-title">Robot Rescue Mission</h2>
                <div class="instructions">
                    <p>Answer questions correctly to keep your robot safe!</p>
                    <ul>
                        <li>Each wrong answer lowers the robot</li>
                        <li>5 wrong answers and the robot falls</li>
                        <li>Complete all questions to win</li>
                    </ul>
                </div>
                <button id="start-btn" aria-label="Start Rescue Mission">Start Rescue</button>
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-area" id="game-area">
            <div class="quiz-section">
                <div class="header">
                    <div class="timer" aria-label="Time remaining">⏱️ <span id="time">30</span>s</div>
                    <div class="lives" aria-label="Lives remaining">❤️ <span id="lives">5</span></div>
                    <div class="volume-control" aria-label="Sound effects volume">
                        <label for="volume-slider">🔊</label>
                        <input type="range" id="volume-slider" min="0" max="100" value="30" aria-label="Adjust sound volume">
                        <span id="volume-value">30%</span>
                    </div>
                </div>
                
                <div class="question" id="question" role="heading" aria-level="3"></div>
                <div class="options" id="options" role="radiogroup" aria-label="Answer options"></div>
                <button class="next-btn" id="next-btn" disabled aria-label="Next question">Next Question →</button>
            </div>
            
            <div class="robot-section">
                <div class="scene-container">
                    <!-- Enhanced gallows -->
                    <div class="gallows">
                        <div class="base"></div>
                        <div class="pole"></div>
                        <div class="beam"></div>
                        <div class="support"></div>
                        <div class="rope"></div>
                    </div>
                    
                    <!-- Enhanced robot -->
                    <div class="robot" id="robot">
                        <div class="rope-around-neck"></div>
                        <div class="head">
                            <div class="antenna"></div>
                            <div class="eyes">
                                <div class="eye left">
                                    <div class="pupil"></div>
                                    <div class="shine"></div>
                                </div>
                                <div class="eye right">
                                    <div class="pupil"></div>
                                    <div class="shine"></div>
                                </div>
                            </div>
                            <div class="mouth">
                                <div class="teeth"></div>
                            </div>
                        </div>
                        <div class="body">
                            <div class="panel"></div>
                            <div class="buttons">
                                <div class="button"></div>
                                <div class="button"></div>
                                <div class="button"></div>
                            </div>
                        </div>
                        <div class="arms">
                            <div class="arm left">
                                <div class="joint elbow"></div>
                                <div class="hand left">
                                    <div class="finger"></div>
                                    <div class="finger"></div>
                                    <div class="finger"></div>
                                </div>
                            </div>
                            <div class="arm right">
                                <div class="joint elbow"></div>
                                <div class="hand right">
                                    <div class="finger"></div>
                                    <div class="finger"></div>
                                    <div class="finger"></div>
                                </div>
                            </div>
                        </div>
                        <div class="legs">
                            <div class="leg left">
                                <div class="joint knee"></div>
                                <div class="foot left"></div>
                            </div>
                            <div class="leg right">
                                <div class="joint knee"></div>
                                <div class="foot right"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script src="../js/game2.js"></script>
</body>
</html>