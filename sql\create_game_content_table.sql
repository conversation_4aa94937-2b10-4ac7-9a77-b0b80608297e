-- Create game_content table as specified by user
CREATE TABLE IF NOT EXISTS game_content (
    content_id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    level_number INT(11) NOT NULL,
    question_text BLOB NOT NULL,
    option1 BLOB NOT NULL,
    option2 BLOB NOT NULL,
    option3 BLOB NOT NULL,
    option4 BLOB NOT NULL,
    correct_answer INT(11) NOT NULL CHECK (correct_answer BETWEEN 1 AND 4),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_level_number (level_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing
INSERT INTO game_content (level_number, question_text, option1, option2, option3, option4, correct_answer) VALUES
(1, 'What is the capital of France?', 'London', 'Paris', 'Berlin', 'Madrid', 2),
(1, 'Which planet is known as the Red Planet?', 'Venus', 'Mars', 'Jupiter', 'Saturn', 2),
(1, 'What is 2 + 2?', '3', '4', '5', '6', 2),
(2, 'Which of these is not a programming language?', 'Python', 'Java', 'HTML', 'C++', 3),
(2, 'What is the largest mammal?', 'Elephant', 'Blue Whale', 'Giraffe', 'Hippopotamus', 2),
(2, 'What does HTTP stand for?', 'HyperText Transfer Protocol', 'High Tech Transfer Protocol', 'Home Tool Transfer Protocol', 'Host Transfer Text Protocol', 1),
(3, 'What does VPN stand for?', 'Virtual Private Network', 'Very Private Network', 'Virtual Public Network', 'Very Public Network', 1),
(3, 'Which device connects multiple computers in a LAN?', 'Router', 'Switch', 'Hub', 'Modem', 2),
(3, 'What is the time complexity of binary search?', 'O(n)', 'O(log n)', 'O(n²)', 'O(1)', 2);
