-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Apr 25, 2025 at 03:15 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `dbcablequest`
--

-- --------------------------------------------------------

--
-- Table structure for table `achievements`
--

CREATE TABLE `achievements` (
  `AchievementID` int(11) NOT NULL,
  `AchievementName` varchar(255) NOT NULL,
  `AchievementDesc` text DEFAULT NULL,
  `ExperienceReward` int(11) DEFAULT 0,
  `Islocked` tinyint(1) DEFAULT 0,
  `Criteria` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `levels`
--

CREATE TABLE `levels` (
  `levelID` int(11) NOT NULL,
  `levelName` varchar(255) DEFAULT NULL,
  `IsActive` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `levels`
--

INSERT INTO `levels` (`levelID`, `levelName`, `IsActive`) VALUES
(1, NULL, 0),
(2, NULL, 0),
(3, NULL, 0),
(4, NULL, 0),
(5, NULL, 0),
(6, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `tier`
--

CREATE TABLE `tier` (
  `tierID` int(11) NOT NULL,
  `tierName` varchar(255) DEFAULT NULL,
  `tierExp` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tier`
--

INSERT INTO `tier` (`tierID`, `tierName`, `tierExp`) VALUES
(0, NULL, 0),
(1, NULL, 100),
(2, NULL, 200),
(3, NULL, 300),
(4, NULL, 400),
(5, NULL, 500),
(6, NULL, 700),
(7, NULL, 900),
(8, NULL, 1100),
(9, NULL, 1300),
(10, NULL, 1500),
(11, NULL, 1800),
(12, NULL, 2100),
(13, NULL, 2400),
(14, NULL, 2700),
(15, NULL, 3000),
(16, NULL, 3400),
(17, NULL, 3800),
(18, NULL, 4200),
(19, NULL, 4600),
(20, NULL, 5000);

-- --------------------------------------------------------

--
-- Table structure for table `user_account`
--

CREATE TABLE `user_account` (
  `user_id` double NOT NULL,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `email` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_account`
--

INSERT INTO `user_account` (`user_id`, `username`, `password`, `created_at`, `email`, `name`) VALUES
(571483, '1', '1', '2025-04-23 00:29:45', NULL, NULL),
(603295, 'bird', '1', '2025-04-23 00:50:32', NULL, NULL),
(863910, 'genrey', '1', '2025-04-23 00:57:26', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_levels`
--

CREATE TABLE `user_levels` (
  `user_id` int(11) DEFAULT NULL,
  `levelID` int(11) DEFAULT NULL,
  `isUnlocked` tinyint(1) DEFAULT 0,
  `levelScore` int(11) DEFAULT NULL,
  `levelStar` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_levels`
--

INSERT INTO `user_levels` (`user_id`, `levelID`, `isUnlocked`, `levelScore`, `levelStar`) VALUES
(571483, 1, 1, 20, 1),
(571483, 2, 1, 100, 3),
(571483, 3, 1, 29, 1),
(571483, 4, 1, NULL, NULL),
(571483, 5, 0, NULL, NULL),
(571483, 6, 0, NULL, NULL),
(603295, 1, 1, NULL, NULL),
(603295, 2, 0, NULL, NULL),
(603295, 3, 0, NULL, NULL),
(603295, 4, 0, NULL, NULL),
(603295, 5, 0, NULL, NULL),
(603295, 6, 0, NULL, NULL),
(863910, 1, 1, 10, 1),
(863910, 2, 1, NULL, NULL),
(863910, 3, 0, NULL, NULL),
(863910, 4, 0, NULL, NULL),
(863910, 5, 0, NULL, NULL),
(863910, 6, 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_tier`
--

CREATE TABLE `user_tier` (
  `userTierID` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `tierID` int(11) DEFAULT NULL,
  `userExp` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_tier`
--

INSERT INTO `user_tier` (`userTierID`, `user_id`, `tierID`, `userExp`) VALUES
(NULL, 571483, 0, 220),
(NULL, 603295, 0, 0),
(NULL, 863910, 0, 40);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `achievements`
--
ALTER TABLE `achievements`
  ADD PRIMARY KEY (`AchievementID`);

--
-- Indexes for table `levels`
--
ALTER TABLE `levels`
  ADD PRIMARY KEY (`levelID`);

--
-- Indexes for table `tier`
--
ALTER TABLE `tier`
  ADD PRIMARY KEY (`tierID`);

--
-- Indexes for table `user_account`
--
ALTER TABLE `user_account`
  ADD PRIMARY KEY (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `achievements`
--
ALTER TABLE `achievements`
  MODIFY `AchievementID` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
