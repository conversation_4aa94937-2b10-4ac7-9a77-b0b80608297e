// Game state
const gameState = {
    playerMaxHealth: 100,
    playerHealth: 100,
    enemyMaxHealth: 150,
    enemyHealth: 150,
    currentQuestion: 0,
    timer: null,
    timeLeft: 10,
    isGameActive: false,
    correctAnswers: 0,
    totalQuestions: 0
};

// Store questions from database
let gameData = [];

// Sound Effects
const correctSound = new Audio('../sounds/correct2.mp3');
const wrongSound = new Audio('../sounds/wrong2.mp3');
const successSound = new Audio('../sounds/success.mp3');

// Set initial volume for all sound effects
correctSound.volume = 0.3;
wrongSound.volume = 0.3;
successSound.volume = 0.3;

// DOM Elements
const questionText = document.querySelector('.question-text');
const optionButtons = document.querySelectorAll('.option-btn');
const timerBar = document.querySelector('.timer-bar');
const timerText = document.querySelector('.timer-text');
const playerHealthBar = document.querySelector('.player-health');
const enemyHealthBar = document.querySelector('.enemy-health');
const playerHealthValue = document.querySelector('.player-side .health-value');
const enemyHealthValue = document.querySelector('.enemy-side .health-value');
const playerRobot = document.querySelector('.player-robot');
const enemyRobot = document.querySelector('.enemy-robot');
const instructionsModal = document.querySelector('.instructions-modal');
const resultsModal = document.querySelector('.results-modal');
const resultTitle = document.querySelector('.result-title');
const resultMessage = document.querySelector('.result-message');
const startButton = document.querySelector('.start-btn');
const replayButton = document.querySelector('.replay-btn');

// Initialize game
async function initGame() {
    try {
        // Fetch questions from the database
        const response = await fetch('../php/game2.php');
        const result = await response.json();

        console.log('Fetched data:', result); // Debug log

        if (result.success) {
            gameData = result.data;
            console.log('Game data loaded:', gameData); // Debug log

            if (gameData.length === 0) {
                console.error('No questions found in the database');
                alert('No questions found. Please try again later.');
                return;
            }

            // Initialize game state
            gameState.playerHealth = gameState.playerMaxHealth;
            gameState.enemyHealth = gameState.enemyMaxHealth;
            gameState.currentQuestion = 0;
            gameState.isGameActive = false;
            gameState.correctAnswers = 0;
            gameState.totalQuestions = gameData.length;

            updateHealthBars();
            loadQuestion();
            // Don't start timer here - wait for start button click

            // Reset timer display
            gameState.timeLeft = 10;
            timerText.textContent = gameState.timeLeft;
            timerBar.style.width = '100%';

            // Show instructions modal on first load
            instructionsModal.style.display = 'flex';
        } else {
            console.error('Failed to load game data:', result.error);
            alert('Failed to load game data. Please try again later.');
        }
    } catch (error) {
        console.error('Error loading game data:', error);
        alert('Error loading game data. Please try again later.');
    }
}

// Load question
function loadQuestion() {
    // Check if we have questions loaded
    if (gameData.length === 0) {
        console.error('No questions available');
        return;
    }

    // Check if we've completed all questions
    if (gameState.currentQuestion >= gameData.length) {
        endGame(true); // Player wins if they complete all questions
        return;
    }

    const currentQ = gameData[gameState.currentQuestion];
    console.log('Current question:', currentQ); // Debug log

    if (!currentQ || !currentQ.question_text) {
        console.error('Invalid question data:', currentQ);
        return;
    }

    questionText.textContent = currentQ.question_text;

    // Create options from the database fields
    const options = [
        currentQ.option1,
        currentQ.option2,
        currentQ.option3,
        currentQ.option4
    ];

    optionButtons.forEach((button, index) => {
        if (!options[index]) {
            console.error('Missing option at index:', index);
            return;
        }

        button.textContent = options[index];
        button.disabled = false;
        button.classList.remove('correct', 'incorrect');
    });
}

// Start timer
function startTimer() {
    gameState.timeLeft = 10;
    timerText.textContent = gameState.timeLeft;
    timerBar.style.width = '100%';

    if (gameState.timer) {
        clearInterval(gameState.timer);
    }

    gameState.timer = setInterval(() => {
        gameState.timeLeft--;
        timerText.textContent = gameState.timeLeft;
        timerBar.style.width = `${(gameState.timeLeft / 10) * 100}%`;

        if (gameState.timeLeft <= 0) {
            clearInterval(gameState.timer);
            handleTimeout();
        }
    }, 1000);
}

// Update health bars
function updateHealthBars() {
    // Update visual health bars
    playerHealthBar.style.width = `${(gameState.playerHealth / gameState.playerMaxHealth) * 100}%`;
    enemyHealthBar.style.width = `${(gameState.enemyHealth / gameState.enemyMaxHealth) * 100}%`;

    // Update health text values
    playerHealthValue.textContent = `${gameState.playerHealth}/${gameState.playerMaxHealth}`;
    enemyHealthValue.textContent = `${gameState.enemyHealth}/${gameState.enemyMaxHealth}`;

    // Check for game end conditions
    if (gameState.playerHealth <= 0) {
        endGame(false);
    } else if (gameState.enemyHealth <= 0) {
        endGame(true);
    }
}

// Handle player attack
function playerAttack() {
    // Play attack animation
    playerRobot.classList.add('attack-animation');

    // Calculate damage (could be adjusted for difficulty)
    const damage = 20;

    // Apply damage after animation delay
    setTimeout(() => {
        // Apply damage to enemy
        gameState.enemyHealth -= damage;

        // Show damage effect on enemy
        enemyRobot.querySelector('.damage-effect').style.opacity = 1;

        // Update health bars
        updateHealthBars();

        // Reset animations
        setTimeout(() => {
            playerRobot.classList.remove('attack-animation');
            enemyRobot.querySelector('.damage-effect').style.opacity = 0;

            // Move to next question if game still active
            if (gameState.isGameActive) {
                gameState.currentQuestion++;
                loadQuestion();
                startTimer();
            }
        }, 500);
    }, 300);
}

// Handle enemy attack
function enemyAttack() {
    // Play attack animation
    enemyRobot.classList.add('enemy-attack-animation');

    // Calculate damage (could be adjusted for difficulty)
    const damage = 20;

    // Apply damage after animation delay
    setTimeout(() => {
        // Apply damage to player
        gameState.playerHealth -= damage;

        // Show damage effect on player
        playerRobot.querySelector('.damage-effect').style.opacity = 1;

        // Update health bars
        updateHealthBars();

        // Reset animations
        setTimeout(() => {
            enemyRobot.classList.remove('enemy-attack-animation');
            playerRobot.querySelector('.damage-effect').style.opacity = 0;

            // Move to next question if game still active
            if (gameState.isGameActive) {
                gameState.currentQuestion++;
                loadQuestion();
                startTimer();
            }
        }, 500);
    }, 300);
}

// Handle answer selection
function handleAnswer(index) {
    // Prevent multiple answers
    if (!gameState.isGameActive) return;

    // Disable all buttons
    optionButtons.forEach(btn => btn.disabled = true);

    // Stop the timer
    clearInterval(gameState.timer);

    // Get current question
    const currentQ = gameData[gameState.currentQuestion];

    // Get the correct answer index (subtracting 1 since database uses 1-based indexing)
    const correctAnswerIndex = parseInt(currentQ.correct_answer) - 1;

    // Check if answer is correct
    if (index === correctAnswerIndex) {
        // Highlight correct answer
        optionButtons[index].classList.add('correct');

        // Increment correct answers
        gameState.correctAnswers++;

        // Play correct sound
        playSound(correctSound);

        // Increase user exp by 10 for correct answer
        const expEarned = 10;

        // Send exp data to savetodb.php
        fetch('../php/savetodb.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expEarned: expEarned,
                score: 0, // We'll update the final score at the end
                starsCount: 0, // We'll update the stars at the end
                level: parseInt(document.body.dataset.level) // Get level from body data-level attribute
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Failed to save exp:', data.error);
            }
        })
        .catch(error => {
            console.error('Error saving exp:', error);
        });

        // Player attacks enemy
        playerAttack();
    } else {
        // Highlight correct and incorrect answers
        optionButtons[index].classList.add('incorrect');
        optionButtons[correctAnswerIndex].classList.add('correct');

        // Play wrong sound
        playSound(wrongSound);

        // Enemy attacks player
        enemyAttack();
    }
}

// Handle timeout (no answer selected)
function handleTimeout() {
    // Prevent multiple timeouts
    if (!gameState.isGameActive) return;

    // Disable all buttons
    optionButtons.forEach(btn => btn.disabled = true);

    // Highlight correct answer
    const currentQ = gameData[gameState.currentQuestion];
    // Get the correct answer index (subtracting 1 since database uses 1-based indexing)
    const correctAnswerIndex = parseInt(currentQ.correct_answer) - 1;
    optionButtons[correctAnswerIndex].classList.add('correct');

    // Play wrong sound for timeout
    playSound(wrongSound);

    // Enemy attacks player
    enemyAttack();
}

// Play Sound Effect
function playSound(sound) {
    // Reset the sound to the beginning
    sound.currentTime = 0;
    // Play the sound
    sound.play().catch(e => console.log('Audio play failed:', e));
}

// End game
function endGame(playerWon) {
    // Set game as inactive
    gameState.isGameActive = false;

    // Clear any running timers
    clearInterval(gameState.timer);

    // Show results with proper calculation
    setTimeout(() => {
        showResults(playerWon);
    }, 1000);
}

// Show Results with Star Rating
function showResults(success) {
    const totalQuestions = gameState.totalQuestions;
    const correctAnswers = gameState.correctAnswers;
    const percentage = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;
    const totalExp = correctAnswers * 10; // 10 exp per correct answer

    // Calculate stars (1-3)
    let stars;
    if (!success) {
        stars = 0;
    } else if (percentage >= 80) {
        stars = 3;
    } else if (percentage >= 50) {
        stars = 2;
    } else {
        stars = 1;
    }

    // Update modal content
    const resultTitle = document.getElementById('result-title');
    const resultMessage = document.getElementById('result-message');
    const resultStars = document.getElementById('result-stars');
    const expText = document.querySelector('.exp-text');

    if (success) {
        resultTitle.textContent = 'VICTORY!';
        resultMessage.textContent = `You defeated the enemy robot! Answered ${correctAnswers} out of ${totalQuestions} questions correctly!`;

        // Add celebration animation and success sound
        if (playerRobot) {
            playerRobot.style.animation = 'idle-bounce 1s ease-in-out infinite';
        }
        playSound(successSound);
    } else {
        resultTitle.textContent = 'DEFEAT!';
        resultMessage.textContent = 'Your robot was destroyed! Better luck next time!';
    }

    // Display stars
    if (resultStars) {
        resultStars.innerHTML = '';
        for (let i = 0; i < 3; i++) {
            const star = document.createElement('span');
            star.textContent = i < stars ? '★' : '☆';
            star.style.color = i < stars ? '#ffeb3b' : '#555';
            resultStars.appendChild(star);
        }
    }

    // Update exp display
    if (expText) {
        expText.textContent = `+${totalExp} EXP`;
    }

    // Send final game data to savetodb.php
    fetch('../php/savetodb.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            expEarned: 0, // We already sent exp per question
            score: correctAnswers,
            starsCount: stars,
            level: parseInt(document.body.dataset.level) // Get level from body data-level attribute
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to save final game data:', data.error);
        }
    })
    .catch(error => {
        console.error('Error saving final game data:', error);
    });

    // Show the modal
    resultsModal.style.display = 'flex';
}

// Shuffle array (Fisher-Yates algorithm)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Event Listeners
optionButtons.forEach((button, index) => {
    button.addEventListener('click', () => handleAnswer(index));
});

startButton.addEventListener('click', () => {
    instructionsModal.style.display = 'none';
    gameState.isGameActive = true;
    // Start the timer when the user clicks the start button
    startTimer();
});

replayButton.addEventListener('click', () => {
    resultsModal.style.display = 'none';
    initGame();
});

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', initGame);
