<?php
session_start();
require_once 'dbconnection.php';

$message = false;

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $password = $_POST['password'];
    $username = $_POST['username'];

    try {
        // Initialize database connection
        $database = new Database();
        $conn = $database->getConnection();

        // Prepare and execute
        $stmt = $conn->prepare("SELECT user_id, username, password FROM user_account WHERE username = ? AND password = ?");
        $stmt->execute([$username, $password]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            if ($username === $user['username']) {
                // After successful login
                $_SESSION['userId'] = $user['user_id'];
                header("Location: ../html/mainpage.html");
                exit;
            } else {
                $message = "Invalid username or password";
                echo $message;
            }
        } else {
            $message = true;
            echo '
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Document</title>
                <link rel="stylesheet" href="../bootstrap4/bootstrap.min.css">
                <script src="../bootstrap4/jquery.min.js"></script>
                <script src="../bootstrap4/bootstrap.bundle.min.js"></script>
                <link rel="stylesheet" href="../css/style_login_signup.css">
            </head>
            <body>
            <button type="button" class="btn btn-primary d-none" id="warning_modal" data-toggle="modal" data-target="#exampleModalCenter">
                    Launch demo modal
                </button>
          
          <div class="modal fade" id="exampleModalCenter" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
              <div class="modal-content bg-dark text-white">
                <div class="modal-header">
                  <h5 class="modal-title" id="exampleModalLongTitle">Warning </h5>
                </div>
                <div class="modal-body">
                  Invalid Username or Password!!
                </div>
                <div class="modal-footer">
                  <button type="button" onclick="returnButton()" class="btn btn-primary">Return</button>
                </div>
              </div>
            </div>
          </div>
            
                <script src="../bootstrap4/jquery.min.js"></script>
                <script src="../bootstrap4/bootstrap.bundle.min.js"></script>

                <script>
                $(document).ready(function(){
                    $("#signupButton").click(function(){
                        alert("Redirecting to Sign Up page...");
                    });
                });
                </script>
            </body>
            </html>';
        }
    } catch(PDOException $e) {
        $message = "Database error: " . $e->getMessage();
        echo $message;
    }
}
?>

<script>
    let login_fail = `<?php echo $message; ?>`;
    console.log(login_fail);
    if (login_fail === "1") {
        triggerButton();
    }

    function triggerButton() {
        document.getElementById("warning_modal").click();
    };

    function returnButton() {
        window.location.href = '../html/login.html'
    }
</script>