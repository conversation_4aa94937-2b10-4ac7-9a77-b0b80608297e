<?php
session_start();
header('Content-Type: application/json');

require_once 'dbconnection.php';

// Get the POST data
$data = json_decode(file_get_contents('php://input'), true);

// Validate required data
// if (!isset($_SESSION['userId'], $data['score'], $data['starsCount'], $data['level'])) {
//     echo json_encode(['success' => false, 'error' => 'Missing required data']);
//     exit;
// }

$score = $data['score'];
$star = $data['starsCount'];
$userId = $_SESSION['userId'];
$expEarned = $data['expEarned'] ?? 0;
$level = $data['level'];
$newLevel = $level + 1;

try {
    $database = new Database();
    $pdo = $database->getConnection();

    // Start transaction
    $pdo->beginTransaction();

    $stme = $pdo->prepare('SELECT userExp FROM user_exp WHERE user_id = ?');
    $stme->execute([$userId]);
    $userExp = $stme->fetchColumn();
    
    if ($userExp === false) {
        // No record exists - initialize with expEarned
        $newExp = $expEarned;
        $stme = $pdo->prepare('INSERT INTO user_exp (user_id, userExp) VALUES (?, ?)');
        $stme->execute([$userId, $expEarned]);
    } else {
        // Record exists - update it    
        $newExp = $userExp + $expEarned;
        $stme = $pdo->prepare('UPDATE user_exp SET userExp = ? WHERE user_id = ?');
        $stme->execute([$newExp, $userId]);
    }
    
    // Check if record exists for current level
    $stmt = $pdo->prepare('SELECT COUNT(*) FROM user_levels WHERE user_id = ? AND level_number = ?');
    $stmt->execute([$userId, $level]);
    $exists = $stmt->fetchColumn();
    
    if ($exists) {
        // Update existing record for current level
        $stmt = $pdo->prepare('UPDATE user_levels SET levelScore = ?, levelStar = ? WHERE user_id = ? AND level_number = ?');
        $stmt->execute([$score, $star, $userId, $level]);
    } else {
        // Insert new record for current level
        $stmt = $pdo->prepare('INSERT INTO user_levels (levelScore, levelStar, user_id, level_number) VALUES (?, ?, ?, ?)');
        $stmt->execute([$score, $star, $userId, $level]);
    }
    
    // Check if next level exists in user's records
    $stmt = $pdo->prepare('SELECT COUNT(*) FROM user_levels WHERE user_id = ? AND level_number = ?');
    $stmt->execute([$userId, $newLevel]);
    $nextLevelExists = $stmt->fetchColumn();
    
    if ($nextLevelExists) {
        // Update isUnlocked for next level if record exists
        $stmt = $pdo->prepare('UPDATE user_levels SET isUnlocked = 1 WHERE user_id = ? AND level_number = ?');
        $stmt->execute([$userId, $newLevel]);
    } else {
        // Insert new record for next level with isUnlocked = 1
        $stmt = $pdo->prepare('INSERT INTO user_levels (user_id, level_number, isUnlocked) VALUES (?, ?, 1)');
        $stmt->execute([$userId, $newLevel]);
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    // Rollback on error
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}