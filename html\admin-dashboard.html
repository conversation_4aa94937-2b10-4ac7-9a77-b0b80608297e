
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="stylesheet" href="../css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Admin Panel</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="#" onclick="showSection('users', this)">
                        <i class="fas fa-users"></i> Users Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" onclick="showSection('content', this)">
                        <i class="fas fa-gamepad"></i> Game Content
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Users Management Section -->
            <section id="users-section" class="content-section active">
                <div class="section-header">
                    <h1>Users Management</h1>
                    <div class="search-bar">
                        <input type="text" id="userSearch" placeholder="Search users..." onkeyup="searchUsers()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>

                <!-- Users Grid -->
                <div class="users-grid" id="usersGrid">
                    <!-- Users will be populated by JavaScript -->
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <button id="prevBtn" onclick="changePage(-1)"><i class="fas fa-chevron-left"></i></button>
                    <span id="pageInfo">Page 1 of 1</span>
                    <button id="nextBtn" onclick="changePage(1)"><i class="fas fa-chevron-right"></i></button>
                </div>
            </section>

            <!-- Game Content Management Section -->
            <section id="content-section" class="content-section">
                <div class="section-header">
                    <h1>Game Content Management</h1>
                    <div class="content-controls">
                        <div class="content-info">
                            <span class="info-text">Manage game levels and their questions</span>
                        </div>
                        <div class="level-controls">
                            <button class="btn-secondary" id="toggleAllLevels" onclick="toggleAllLevels()">
                                <i class="fas fa-expand-alt"></i> Expand All
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Game Levels List -->
                <div class="levels-container" id="levelsContainer">
                    <!-- Levels will be populated by JavaScript -->
                </div>
            </section>
        </main>
    </div>

    <!-- User Details Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>User Details</h2>
                <span class="close" onclick="closeModal('userModal')">&times;</span>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- User details will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Question Modal -->
    <div id="questionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="questionModalTitle">Add Question</h2>
                <span class="close" onclick="closeModal('questionModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="questionForm">
                    <div class="form-group">
                        <label for="questionText">Question:</label>
                        <textarea id="questionText" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="option1">Option A:</label>
                        <input type="text" id="option1" required>
                    </div>
                    <div class="form-group">
                        <label for="option2">Option B:</label>
                        <input type="text" id="option2" required>
                    </div>
                    <div class="form-group">
                        <label for="option3">Option C:</label>
                        <input type="text" id="option3" required>
                    </div>
                    <div class="form-group">
                        <label for="option4">Option D:</label>
                        <input type="text" id="option4" required>
                    </div>
                    <div class="form-group">
                        <label for="correctAnswer">Correct Answer:</label>
                        <select id="correctAnswer" required>
                            <option value="">Select correct answer</option>
                            <option value="1">Option A</option>
                            <option value="2">Option B</option>
                            <option value="3">Option C</option>
                            <option value="4">Option D</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="levelNumber">Level Number:</label>
                        <input type="number" id="levelNumber" min="1" required>
                    </div>
                    <input type="hidden" id="contentId" value="">
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('questionModal')">Cancel</button>
                        <button type="submit" class="btn-primary">Save Question</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/admin/admin-dashboard.js"></script>
</body>
</html>

