<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tier System Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .tier-display {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Tier System Debug Tool</h1>
    
    <div class="debug-section">
        <h2>Current Tier Display</h2>
        <div class="tier-display">
            Tier: <span id="user_tier">Loading...</span>
            <br>
            <small id="user_tier_name">Loading...</small>
        </div>
    </div>
    
    <div class="debug-section">
        <h2>API Tests</h2>
        <button onclick="testUserExp()">Test Get User Exp</button>
        <button onclick="testTierTable()">Test Get Tier Table</button>
        <button onclick="testFullTierCalculation()">Test Full Tier Calculation</button>
        <div id="apiResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="debug-section">
        <h2>Database Debug</h2>
        <button onclick="checkUserTierTable()">Check User Tier Table</button>
        <button onclick="checkTierTable()">Check Tier Table</button>
        <div id="dbResult" class="result" style="display: none;"></div>
    </div>

    <script>
        async function testUserExp() {
            try {
                const response = await fetch('../php/get_user_exp.php', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                showResult('apiResult', data, response.ok);
            } catch (error) {
                showResult('apiResult', { error: error.message }, false);
            }
        }

        async function testTierTable() {
            try {
                const response = await fetch('../php/get_tier_table.php', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                showResult('apiResult', data, response.ok);
            } catch (error) {
                showResult('apiResult', { error: error.message }, false);
            }
        }

        async function testFullTierCalculation() {
            try {
                // Import the function from mainpage.js
                const result = await fetchAndDisplayTier();
                showResult('apiResult', result || { message: 'Function executed, check console for details' }, true);
            } catch (error) {
                showResult('apiResult', { error: error.message }, false);
            }
        }

        async function checkUserTierTable() {
            try {
                const response = await fetch('../php/debug_user_tier.php', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                showResult('dbResult', data, response.ok);
            } catch (error) {
                showResult('dbResult', { error: error.message }, false);
            }
        }

        async function checkTierTable() {
            try {
                const response = await fetch('../php/debug_tier_table.php', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                showResult('dbResult', data, response.ok);
            } catch (error) {
                showResult('dbResult', { error: error.message }, false);
            }
        }

        function showResult(elementId, data, isSuccess) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        // Load the mainpage.js functions
        async function loadMainPageScript() {
            try {
                const script = document.createElement('script');
                script.src = '../js/mainpage.js';
                document.head.appendChild(script);
                
                // Wait a bit for the script to load
                setTimeout(() => {
                    if (typeof fetchAndDisplayTier === 'function') {
                        console.log('fetchAndDisplayTier function loaded successfully');
                        // Auto-run the tier calculation
                        fetchAndDisplayTier();
                    } else {
                        console.error('fetchAndDisplayTier function not found');
                    }
                }, 500);
            } catch (error) {
                console.error('Error loading mainpage.js:', error);
            }
        }

        // Load the script when page loads
        document.addEventListener('DOMContentLoaded', loadMainPageScript);
    </script>
</body>
</html>
