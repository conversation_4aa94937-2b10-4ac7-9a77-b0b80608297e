<?php
// Ensure no output before headers
ob_start();
header('Content-Type: application/json');

// Error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

require_once 'dbconnection.php';

session_start();

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

$userId = $_SESSION['userId'];

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON input');
    }

    // Validate required fields
    if (!isset($input['email']) || !isset($input['bio'])) {
        throw new Exception('Missing required fields');
    }

    // Validate and sanitize input
    $email = filter_var($input['email'], FILTER_SANITIZE_EMAIL);
    $bio = filter_var($input['bio'], FILTER_SANITIZE_STRING);
    $avatar = isset($input['avatar']) ? filter_var($input['avatar'], FILTER_SANITIZE_URL) : null;

    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }

    // Prepare and execute update query
    if ($avatar) {
        // Update with avatar
        $stmt = $conn->prepare("UPDATE user_account SET email = :email, bio = :bio, avatar = :avatar WHERE user_id = :user_id");
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':bio', $bio, PDO::PARAM_STR);
        $stmt->bindParam(':avatar', $avatar, PDO::PARAM_STR);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    } else {
        // Update without avatar
        $stmt = $conn->prepare("UPDATE user_account SET email = :email, bio = :bio WHERE user_id = :user_id");
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':bio', $bio, PDO::PARAM_STR);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    }

    // Execute and respond
    if ($stmt->execute()) {
        $response = ['success' => true, 'message' => 'Profile saved successfully'];
    } else {
        throw new Exception('Failed to save profile');
    }
    
} catch (PDOException $e) {
    $response = ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
} catch (Exception $e) {
    $response = ['success' => false, 'message' => $e->getMessage()];
}

echo json_encode($response);
ob_end_flush();