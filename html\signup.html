<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

      <!-- Bootstrap 4 CSS -->
      <link rel="stylesheet" href="../bootstrap4/bootstrap.min.css">
      <script src="../bootstrap4/jquery.min.js"></script>
      <script src="../bootstrap4/bootstrap.bundle.min.js"></script>
  
      <link rel="stylesheet" href="../css/style_login_signup.css">
</head>
<body>

    <div class="header">
        <p class="title"><a href="homepage.html">CABLE QUEST</a></p>
    </div>

    <div class="body">
        <div class="signup-cont">
            
            <p class="signup-title">Create Account</p>

            <form action="../php/signup.php" id="signup_form" method="post">
                <label for="username"><p>Player name</p></label>
                <input type="text" id="username" name="username" placeholder="" required>
                <label for="password" id="passwod_lbl"><p>Password</p></label>
                <input type="password" id="password" name="passwod" placeholder="" required>
                <label for="confirmpass" id="confirmpass_lbl"><p>Confirm Password</p></label>
                <input type="password" id="confirmpass" name="password" placeholder="" required>
                <div class="btn-cont">
                    <a href="login.html"><p>Already have an account???</p></a>
                    <button type="submit" id="signup">Enter</button>
                </div>
            </form>

        </div>
    </div>

    <!-- Button trigger modal -->
<button type="button" class="btn btn-primary d-none" id="success_modal" data-toggle="modal" data-target="#exampleModalCenter">
    Launch demo modal
  </button>

    <div class="modal fade" id="exampleModalCenter" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-content bg-dark text-white">
            <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLongTitle">Account will be created after confirmation </h5>

            </div>
            <div class="modal-body">
              Are you sure you want to create an account?
            </div>
            <div class="modal-footer">
              <button type="button" onclick="createAcc()" class="btn btn-primary">Yes</button>
              <button type="button" class="btn bg-light" data-dismiss="modal">No</button>
            </div>
          </div>
        </div>

    <script src="../js/signup.js"></script>

    <!-- jQuery (Offline) & Bootstrap JS -->
    <script src="../bootstrap4/jquery.min.js"></script>
    <script src="../bootstrap4/bootstrap.bundle.min.js"></script>

    <!-- jQuery Script for Sign Up Button -->
    <script>
    $(document).ready(function(){
        $("#signupButton").click(function(){
            alert("Redirecting to Sign Up page...");
            // window.location.href = "signup.html"; // Uncomment to redirect
        });
    });
    </script>
</body>
</html>