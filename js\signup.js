document.getElementById("signup").addEventListener("click", function(event){
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const confirm_password = document.getElementById('confirmpass').value;

    if (username !== "" && password !== "" && confirm_password !== "") {
        event.preventDefault()
        confirmpass();
    }

  });
  

function confirmpass() {
    const password = document.getElementById('password').value;
    const confirm_password = document.getElementById('confirmpass').value;

    if (password !== confirm_password) {
        document.getElementById('confirmpass_lbl').style.color = "red";
        document.getElementById('confirmpass_lbl').innerHTML = "Passwords do not match!";
        document.getElementById('passwod_lbl').style.color = "red";
        document.getElementById('passwod_lbl').innerHTML = "Passwords do not match!";
    } else {
        document.getElementById("success_modal").click();
    }
}


function createAcc() {
    document.getElementById('signup_form').submit(); 
}
